#!/usr/bin/env python3
"""
Web Scraper Script

A simple web scraping utility for extracting data from websites.
"""

import requests
from bs4 import BeautifulSoup
import json
import time
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class WebScraper:
    """网页爬虫类"""
    
    def __init__(self, headers: Optional[Dict[str, str]] = None):
        """
        初始化爬虫
        
        Args:
            headers: 请求头，默认使用常见的浏览器头
        """
        self.session = requests.Session()
        self.headers = headers or {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session.headers.update(self.headers)
    
    def get_page(self, url: str, timeout: int = 10) -> Optional[BeautifulSoup]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            timeout: 超时时间（秒）
            
        Returns:
            BeautifulSoup对象或None
        """
        try:
            logger.info(f"正在获取页面: {url}")
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            logger.info(f"成功获取页面，标题: {soup.title.string if soup.title else '无标题'}")
            return soup
            
        except requests.RequestException as e:
            logger.error(f"获取页面失败: {e}")
            return None
    
    def extract_links(self, soup: BeautifulSoup, base_url: str = "") -> List[str]:
        """
        提取页面中的所有链接
        
        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL，用于处理相对链接
            
        Returns:
            链接列表
        """
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            if href.startswith('http'):
                links.append(href)
            elif base_url and href.startswith('/'):
                links.append(base_url + href)
        
        return links
    
    def extract_text_content(self, soup: BeautifulSoup, selector: str = "body") -> str:
        """
        提取指定选择器的文本内容
        
        Args:
            soup: BeautifulSoup对象
            selector: CSS选择器
            
        Returns:
            提取的文本内容
        """
        element = soup.select_one(selector)
        if element:
            return element.get_text(strip=True)
        return ""
    
    def save_data(self, data: Dict, filename: str):
        """
        保存数据到JSON文件
        
        Args:
            data: 要保存的数据
            filename: 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")


def main():
    """主函数示例"""
    scraper = WebScraper()
    
    # 示例：爬取一个网页
    url = "https://httpbin.org/html"
    soup = scraper.get_page(url)
    
    if soup:
        # 提取标题
        title = scraper.extract_text_content(soup, "h1")
        print(f"页面标题: {title}")
        
        # 提取所有链接
        links = scraper.extract_links(soup, url)
        print(f"找到 {len(links)} 个链接")
        
        # 保存数据
        data = {
            "url": url,
            "title": title,
            "links": links,
            "timestamp": time.time()
        }
        scraper.save_data(data, "scraped_data.json")


if __name__ == "__main__":
    main()
