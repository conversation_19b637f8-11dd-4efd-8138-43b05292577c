#!/usr/bin/env python3
"""
测试日历爬虫功能
"""

import json
from calendar_spider import CalendarSpider
from datetime import datetime

def test_hijri_calculation():
    """测试伊斯兰历计算功能"""
    spider = CalendarSpider()
    
    # 测试一些已知的日期
    test_cases = [
        (2025, 1, 1),
        (2025, 3, 1),  # 斋月开始
        (2025, 6, 26),  # 伊斯兰历新年
        (2025, 12, 25),  # 圣诞节
    ]
    
    print("=== 伊斯兰历计算测试 ===")
    for year, month, day in test_cases:
        hijri_date = spider.gregorian_to_hijri(year, month, day)
        print(f"公历 {year}-{month:02d}-{day:02d} -> 伊斯兰历 {hijri_date['day']} {hijri_date['month_name']} {hijri_date['year']}")
        
        # 验证计算
        is_valid = spider.validate_hijri_calculation(year, month, day)
        print(f"  验证结果: {'✓' if is_valid else '✗'}")
    
    print()

def test_month_info():
    """测试月份信息功能"""
    spider = CalendarSpider()
    
    print("=== 伊斯兰历月份信息测试 ===")
    for month_num in range(1, 13):
        month_info = spider.get_hijri_month_info(month_num)
        print(f"月份 {month_num}: {month_info['name']} ({month_info['days']}天)")
    
    print()

def test_event_descriptions():
    """测试节日描述功能"""
    spider = CalendarSpider()
    
    test_events = [
        'Mulai Ramadhan',
        'Hari Raya Idulfitri',
        'Hari Raya Idul Adha',
        'Maulid Nabi Muhammad',
        'Isra Mikraj Nabi Muhammad',
        'Tahun Baru Islam'
    ]
    
    print("=== 伊斯兰历节日描述测试 ===")
    for event in test_events:
        description = spider.get_event_description(event)
        print(f"{event}: {description}")
    
    print()

def test_calendar_data():
    """测试日历数据完整性"""
    spider = CalendarSpider()
    
    print("=== 日历数据完整性测试 ===")
    
    # 抓取2025年数据
    calendar_data = spider.scrape_calendar_mapping(2025)
    
    if not calendar_data:
        print("❌ 未能获取日历数据")
        return
    
    print(f"✓ 成功获取 {len(calendar_data)} 个日期映射")
    
    # 检查数据完整性
    total_days = 0
    events_count = 0
    
    for date, info in calendar_data.items():
        total_days += 1
        
        # 检查必要字段
        required_fields = ['hijri_date', 'hijri_day', 'hijri_month', 'hijri_year', 'hijri_month_name']
        for field in required_fields:
            if field not in info:
                print(f"❌ 缺少字段 {field} 在日期 {date}")
        
        # 统计节日
        if 'event' in info:
            events_count += 1
    
    print(f"✓ 总天数: {total_days}")
    print(f"✓ 伊斯兰历节日数量: {events_count}")
    
    # 检查年份范围
    years = set()
    for info in calendar_data.values():
        years.add(info['hijri_year'])
    
    print(f"✓ 伊斯兰历年份范围: {min(years)} - {max(years)}")
    
    # 显示节日详情
    events = {k: v for k, v in calendar_data.items() if 'event' in v}
    if events:
        print("\n伊斯兰历节日详情:")
        for date, info in events.items():
            print(f"  {date}: {info['event']}")
            if 'description' in info:
                print(f"    描述: {info['description']}")

def main():
    """主测试函数"""
    print("开始测试日历爬虫功能...\n")
    
    try:
        test_hijri_calculation()
        test_month_info()
        test_event_descriptions()
        test_calendar_data()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
