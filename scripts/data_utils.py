#!/usr/bin/env python3
"""
Data Utilities Script

A collection of useful data processing utilities.
"""

import pandas as pd
import json
import csv
from pathlib import Path
from typing import List, Dict, Any, Union
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理工具类"""
    
    def __init__(self):
        """初始化数据处理器"""
        pass
    
    def read_json(self, file_path: str) -> Dict[str, Any]:
        """
        读取JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            解析后的数据字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功读取JSON文件: {file_path}")
            return data
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return {}
    
    def write_json(self, data: Dict[str, Any], file_path: str, indent: int = 2):
        """
        写入JSON文件
        
        Args:
            data: 要写入的数据
            file_path: 文件路径
            indent: 缩进空格数
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=indent)
            logger.info(f"成功写入JSON文件: {file_path}")
        except Exception as e:
            logger.error(f"写入JSON文件失败: {e}")
    
    def read_csv(self, file_path: str, encoding: str = 'utf-8') -> pd.DataFrame:
        """
        读取CSV文件
        
        Args:
            file_path: CSV文件路径
            encoding: 文件编码
            
        Returns:
            pandas DataFrame
        """
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            logger.info(f"成功读取CSV文件: {file_path}, 形状: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            return pd.DataFrame()
    
    def write_csv(self, df: pd.DataFrame, file_path: str, encoding: str = 'utf-8'):
        """
        写入CSV文件
        
        Args:
            df: pandas DataFrame
            file_path: 文件路径
            encoding: 文件编码
        """
        try:
            df.to_csv(file_path, index=False, encoding=encoding)
            logger.info(f"成功写入CSV文件: {file_path}")
        except Exception as e:
            logger.error(f"写入CSV文件失败: {e}")
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        # 删除重复行
        df_cleaned = df.drop_duplicates()
        
        # 删除全为空的行
        df_cleaned = df_cleaned.dropna(how='all')
        
        # 删除全为空的列
        df_cleaned = df_cleaned.dropna(axis=1, how='all')
        
        logger.info(f"数据清洗完成: {df.shape} -> {df_cleaned.shape}")
        return df_cleaned
    
    def filter_data(self, df: pd.DataFrame, conditions: Dict[str, Any]) -> pd.DataFrame:
        """
        根据条件过滤数据
        
        Args:
            df: DataFrame
            conditions: 过滤条件字典，格式: {'column': value}
            
        Returns:
            过滤后的DataFrame
        """
        filtered_df = df.copy()
        
        for column, value in conditions.items():
            if column in filtered_df.columns:
                if isinstance(value, (list, tuple)):
                    filtered_df = filtered_df[filtered_df[column].isin(value)]
                else:
                    filtered_df = filtered_df[filtered_df[column] == value]
        
        logger.info(f"数据过滤完成: {df.shape} -> {filtered_df.shape}")
        return filtered_df
    
    def merge_data(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                   on: Union[str, List[str]], how: str = 'inner') -> pd.DataFrame:
        """
        合并两个DataFrame
        
        Args:
            df1: 第一个DataFrame
            df2: 第二个DataFrame
            on: 合并的键
            how: 合并方式 ('inner', 'outer', 'left', 'right')
            
        Returns:
            合并后的DataFrame
        """
        try:
            merged_df = pd.merge(df1, df2, on=on, how=how)
            logger.info(f"数据合并完成: {df1.shape} + {df2.shape} -> {merged_df.shape}")
            return merged_df
        except Exception as e:
            logger.error(f"数据合并失败: {e}")
            return pd.DataFrame()
    
    def get_summary_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        获取数据摘要统计
        
        Args:
            df: DataFrame
            
        Returns:
            统计信息字典
        """
        stats = {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'missing_values': df.isnull().sum().to_dict(),
            'numeric_summary': df.describe().to_dict() if df.select_dtypes(include=['number']).shape[1] > 0 else {}
        }
        
        return stats


def main():
    """主函数示例"""
    processor = DataProcessor()
    
    # 创建示例数据
    sample_data = {
        'name': ['Alice', 'Bob', 'Charlie', 'David'],
        'age': [25, 30, 35, 28],
        'city': ['Beijing', 'Shanghai', 'Guangzhou', 'Shenzhen']
    }
    
    # 转换为DataFrame
    df = pd.DataFrame(sample_data)
    print("原始数据:")
    print(df)
    print()
    
    # 保存为CSV
    processor.write_csv(df, 'sample_data.csv')
    
    # 读取CSV
    loaded_df = processor.read_csv('sample_data.csv')
    print("读取的数据:")
    print(loaded_df)
    print()
    
    # 获取统计信息
    stats = processor.get_summary_stats(loaded_df)
    print("数据统计:")
    print(json.dumps(stats, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
