#!/usr/bin/env python3
"""
Calendar Spider Script

从 calendar-yearly.com 网站抓取公历和伊斯兰历的对应关系
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, date
import math

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CalendarSpider:
    """日历爬虫类"""
    
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 印尼语月份到数字的映射
        self.month_map = {
            'Januari': 1, 'Februari': 2, 'Maret': 3, 'April': 4, 
            'Mei': 5, 'Juni': 6, 'Juli': 7, '<PERSON>gus<PERSON>': 8, 
            'September': 9, 'Oktober': 10, 'November': 11, 'Desember': 12
        }
        
        # 伊斯兰历月份名称映射
        self.hijri_months = {
            'Muharam': 'Muharram', 'Safar': 'Safar', 'Rabiul Awal': 'Rabi al-Awwal',
            'Rabiul Akhir': 'Rabi al-Thani', 'Jumadil Awal': 'Jumada al-Awwal',
            'Jumadil Akhir': 'Jumada al-Thani', 'Rajab': 'Rajab', 'Syaban': 'Shaban',
            'Ramadan': 'Ramadan', 'Syawal': 'Shawwal', 'Zulkaidah': 'Dhu al-Qadah',
            'Zulhijah': 'Dhu al-Hijjah'
        }
        
        # 伊斯兰历月份数字到名称的映射
        self.hijri_month_names = [
            'Muharram', 'Safar', 'Rabi al-Awwal', 'Rabi al-Thani',
            'Jumada al-Awwal', 'Jumada al-Thani', 'Rajab', 'Shaban',
            'Ramadan', 'Shawwal', 'Dhu al-Qadah', 'Dhu al-Hijjah'
        ]
    
    def gregorian_to_hijri(self, year: int, month: int, day: int) -> Dict:
        """
        将公历日期转换为伊斯兰历日期
        
        Args:
            year: 公历年
            month: 公历月
            day: 公历日
            
        Returns:
            伊斯兰历日期字典
        """
        # 使用改进的算法计算伊斯兰历日期
        # 基于 Julian Day Number 的计算方法
        
        # 计算 Julian Day Number
        if month <= 2:
            year -= 1
            month += 12
        
        jd = int(365.25 * (year + 4716)) + int(30.6001 * (month + 1)) + day - 1524.5
        
        # 伊斯兰历纪元开始于 622年7月16日 (Julian Day Number: 1948086.5)
        hijri_epoch = 1948086.5
        
        # 计算伊斯兰历天数
        hijri_days = int(jd - hijri_epoch)
        
        # 计算伊斯兰历年
        hijri_year = int((30 * hijri_days + 10646) / 10631)
        
        # 计算伊斯兰历月
        hijri_month = int((hijri_days - int((hijri_year - 1) * 10631 / 30)) / 29.5) + 1
        if hijri_month > 12:
            hijri_month = 12
        
        # 计算伊斯兰历日
        hijri_day = hijri_days - int((hijri_year - 1) * 10631 / 30) - int((hijri_month - 1) * 29.5) + 1
        
        return {
            'year': hijri_year,
            'month': hijri_month,
            'day': int(hijri_day),
            'month_name': self.hijri_month_names[hijri_month - 1] if 1 <= hijri_month <= 12 else f"Month_{hijri_month}"
        }
    
    def get_calendar_page(self, year: int) -> Optional[BeautifulSoup]:
        """
        获取指定年份的日历页面
        
        Args:
            year: 年份
            
        Returns:
            BeautifulSoup对象或None
        """
        url = f"https://calendar-yearly.com/id/{year}"
        logger.info(f"正在获取 {year} 年日历页面: {url}")
        
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            logger.info(f"成功获取页面")
            
            return soup
            
        except requests.RequestException as e:
            logger.error(f"获取页面失败: {e}")
            return None
    
    def extract_month_data(self, month_section: BeautifulSoup, year: int) -> List[Dict]:
        """
        从月份部分提取日历数据
        
        Args:
            month_section: 月份部分的BeautifulSoup对象
            year: 公历年
            
        Returns:
            该月的日历数据列表
        """
        month_data = []
        
        # 查找月份标题
        month_title = month_section.find('h2')
        if not month_title:
            return month_data
            
        month_text = month_title.get_text(strip=True)
        month_match = re.search(r'(\w+)\s+\d{4}', month_text)
        if not month_match:
            return month_data
            
        month_name = month_match.group(1)
        month_num = self.month_map.get(month_name)
        if not month_num:
            logger.warning(f"未知月份: {month_name}")
            return month_data
        
        # 查找日历网格
        calendar_grids = month_section.find_all('div', class_='grid grid-cols-8')
        
        for grid in calendar_grids:
            # 跳过表头行
            if 'bg-black' in str(grid):
                continue
                
            # 获取该行的所有单元格
            cells = grid.find_all('div', class_='text-center')
            
            for cell in cells:
                # 检查是否有日期内容
                day_div = cell.find('div')
                if not day_div:
                    continue
                    
                day_text = day_div.get_text(strip=True)
                if not day_text or not day_text.isdigit():
                    continue
                    
                day_num = int(day_text)
                
                # 计算对应的伊斯兰历日期
                hijri_date = self.gregorian_to_hijri(year, month_num, day_num)
                
                gregorian_date = f"{year}-{month_num:02d}-{day_num:02d}"
                
                month_data.append({
                    'gregorian_date': gregorian_date,
                    'gregorian_day': day_num,
                    'gregorian_month': month_num,
                    'gregorian_year': year,
                    'hijri_date': f"{hijri_date['day']} {hijri_date['month_name']} {hijri_date['year']}",
                    'hijri_day': hijri_date['day'],
                    'hijri_month': hijri_date['month'],
                    'hijri_year': hijri_date['year'],
                    'hijri_month_name': hijri_date['month_name']
                })
        
        return month_data
    
    def validate_hijri_calculation(self, year: int, month: int, day: int) -> bool:
        """
        验证伊斯兰历计算的准确性
        
        Args:
            year: 公历年
            month: 公历月
            day: 公历日
            
        Returns:
            计算是否合理的布尔值
        """
        hijri_date = self.gregorian_to_hijri(year, month, day)
        
        # 基本验证
        if not (1 <= hijri_date['day'] <= 30):
            return False
        if not (1 <= hijri_date['month'] <= 12):
            return False
        if not (1400 <= hijri_date['year'] <= 1500):  # 合理的年份范围
            return False
            
        return True
    
    def get_hijri_month_info(self, month_num: int) -> Dict:
        """
        获取伊斯兰历月份信息
        
        Args:
            month_num: 伊斯兰历月份数字
            
        Returns:
            月份信息字典
        """
        if 1 <= month_num <= 12:
            return {
                'number': month_num,
                'name': self.hijri_month_names[month_num - 1],
                'days': 30 if month_num in [1, 3, 5, 7, 9, 11] else 29
            }
        return {'number': month_num, 'name': f"Month_{month_num}", 'days': 30}
    
    def extract_islamic_events(self, soup: BeautifulSoup) -> Dict:
        """
        提取伊斯兰历节日信息
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            伊斯兰历节日字典
        """
        events = {}
        
        # 查找包含伊斯兰历节日的文本
        islamic_keywords = [
            'Ramadhan', 'Idulfitri', 'Idul Adha', 'Maulid Nabi', 
            'Isra Mikraj', 'Tahun Baru Islam', 'Muharam'
        ]
        
        # 查找所有包含伊斯兰历关键词的文本
        for keyword in islamic_keywords:
            elements = soup.find_all(string=re.compile(keyword, re.IGNORECASE))
            for element in elements:
                # 获取包含该文本的完整元素
                parent = element.parent
                if parent:
                    text = parent.get_text(strip=True)
                    # 尝试提取日期信息
                    date_match = re.search(r'(\d{1,2})\.(\d{1,2})\.\s*(.+)', text)
                    if date_match:
                        day = int(date_match.group(1))
                        month = int(date_match.group(2))
                        event_name = date_match.group(3).strip()
                        
                        date_key = f"2025-{month:02d}-{day:02d}"
                        events[date_key] = {
                            'event': event_name,
                            'type': 'islamic',
                            'description': self.get_event_description(event_name)
                        }
        
        return events
    
    def get_event_description(self, event_name: str) -> str:
        """
        获取伊斯兰历节日的描述
        
        Args:
            event_name: 节日名称
            
        Returns:
            节日描述
        """
        descriptions = {
            'Ramadhan': '伊斯兰教斋月，穆斯林进行斋戒的月份',
            'Idulfitri': '开斋节，斋月结束后的庆祝节日',
            'Idul Adha': '宰牲节，纪念易卜拉欣献祭的节日',
            'Maulid Nabi': '先知穆罕默德诞辰纪念日',
            'Isra Mikraj': '夜行登宵节，纪念先知夜行和登宵',
            'Tahun Baru Islam': '伊斯兰历新年',
            'Muharam': '伊斯兰历第一个月，包含阿舒拉日'
        }
        
        for key, desc in descriptions.items():
            if key in event_name:
                return desc
        
        return '伊斯兰历重要节日'
    
    def scrape_calendar_mapping(self, year: int) -> Dict:
        """
        抓取指定年份的公历-伊斯兰历映射关系
        
        Args:
            year: 年份
            
        Returns:
            包含映射关系的字典
        """
        soup = self.get_calendar_page(year)
        if not soup:
            return {}
        
        # 查找所有月份部分
        month_sections = soup.find_all('div', class_='mx-2 sm:mx-auto max-w-md bg-white border rounded-xl shadow-md overflow-hidden my-2 dark:bg-gray-600 dark:border-gray-500')
        
        if not month_sections:
            logger.error("无法找到月份数据")
            return {}
        
        logger.info(f"找到 {len(month_sections)} 个月份部分")
        
        # 处理每个月份
        all_calendar_data = []
        for section in month_sections:
            month_data = self.extract_month_data(section, year)
            all_calendar_data.extend(month_data)
        
        # 提取伊斯兰历节日信息
        islamic_events = self.extract_islamic_events(soup)
        
        # 转换为以公历日期为键的字典
        calendar_map = {}
        for data in all_calendar_data:
            gregorian_date = data['gregorian_date']
            
            calendar_map[gregorian_date] = {
                'hijri_date': data['hijri_date'],
                'hijri_day': data['hijri_day'],
                'hijri_month': data['hijri_month'],
                'hijri_year': data['hijri_year'],
                'hijri_month_name': data['hijri_month_name']
            }
            
            # 添加节日信息
            if gregorian_date in islamic_events:
                calendar_map[gregorian_date]['event'] = islamic_events[gregorian_date]['event']
                calendar_map[gregorian_date]['event_type'] = islamic_events[gregorian_date]['type']
        
        logger.info(f"成功提取 {len(calendar_map)} 个日期映射")
        return calendar_map
    
    def save_calendar_data(self, data: Dict, filename: str):
        """
        保存日历数据到JSON文件
        
        Args:
            data: 日历数据
            filename: 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")


def main():
    """主函数"""
    spider = CalendarSpider()
    
    # 抓取2025年的日历数据
    target_year = 2025
    calendar_data = spider.scrape_calendar_mapping(target_year)
    
    if calendar_data:
        print(f"\n=== {target_year}年 公历-伊斯兰历 映射关系 ===")
        
        # 显示前10个映射作为示例
        sample_data = dict(list(calendar_data.items())[:10])
        print(json.dumps(sample_data, indent=2, ensure_ascii=False))
        
        # 保存到文件
        filename = f"calendar_mapping_{target_year}.json"
        spider.save_calendar_data(calendar_data, filename)
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"- 总映射数量: {len(calendar_data)}")
        print(f"- 数据已保存到: {filename}")
        
        # 显示伊斯兰历节日
        events = {k: v for k, v in calendar_data.items() if 'event' in v}
        if events:
            print(f"\n伊斯兰历节日:")
            for date, info in events.items():
                print(f"  {date}: {info['event']}")
    else:
        print("未能获取到日历数据")


if __name__ == "__main__":
    main()
