#!/usr/bin/env python3
"""
Calendar Spider Script

从 calendar-yearly.com 网站抓取公历和伊斯兰历的对应关系
结合网站显示的关键日子和计算方法来获取准确的对应关系
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, date, timedelta
import math

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CalendarSpider:
    """日历爬虫类 - 重写版本，专门解析 calendar-yearly.com 的结构"""

    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # 印尼语月份到数字的映射
        self.month_map = {
            'Januari': 1, 'Februari': 2, 'Maret': 3, 'April': 4,
            'Mei': 5, 'Juni': 6, 'Juli': 7, 'Agustus': 8,
            'September': 9, 'Oktober': 10, 'November': 11, 'Desember': 12
        }

        # 伊斯兰历重要节日和事件的映射
        self.islamic_events = {
            'Tahun Baru Islam': 'Islamic New Year (1 Muharram)',
            'Muharam': 'Muharram Month',
            'Isra Mikraj': 'Isra and Mi\'raj',
            'Mulai Ramadhan': 'Start of Ramadan',
            'Ramadhan': 'Ramadan',
            'Hari Raya Idulfitri': 'Eid al-Fitr',
            'Idulfitri': 'Eid al-Fitr',
            'Hari Raya Idul Adha': 'Eid al-Adha',
            'Idul Adha': 'Eid al-Adha',
            'Maulid Nabi Muhammad': 'Mawlid (Prophet Muhammad\'s Birthday)'
        }

        # 伊斯兰历月份名称
        self.hijri_month_names = [
            'Muharram', 'Safar', 'Rabi al-Awwal', 'Rabi al-Thani',
            'Jumada al-Awwal', 'Jumada al-Thani', 'Rajab', 'Shaban',
            'Ramadan', 'Shawwal', 'Dhu al-Qadah', 'Dhu al-Hijjah'
        ]

        # 关键日期的伊斯兰历对应关系（用于校准计算）
        self.key_dates_2025 = {
            '2025-03-01': {'hijri_month': 9, 'hijri_day': 1, 'event': 'Start of Ramadan'},  # 斋月开始
            '2025-03-30': {'hijri_month': 10, 'hijri_day': 1, 'event': 'Eid al-Fitr'},     # 开斋节
            '2025-06-06': {'hijri_month': 12, 'hijri_day': 10, 'event': 'Eid al-Adha'},   # 宰牲节
            '2025-06-26': {'hijri_month': 1, 'hijri_day': 1, 'event': 'Islamic New Year'}, # 伊斯兰历新年
            '2025-09-04': {'hijri_month': 3, 'hijri_day': 12, 'event': 'Mawlid'},         # 先知诞辰
        }
    
    def gregorian_to_hijri_improved(self, year: int, month: int, day: int) -> Dict:
        """
        改进的公历到伊斯兰历转换算法
        结合关键日期进行校准，提高准确性

        Args:
            year: 公历年
            month: 公历月
            day: 公历日

        Returns:
            伊斯兰历日期字典
        """
        gregorian_date = f"{year}-{month:02d}-{day:02d}"

        # 如果是关键日期，直接返回已知的对应关系
        if gregorian_date in self.key_dates_2025:
            key_info = self.key_dates_2025[gregorian_date]
            return {
                'year': 1446 if gregorian_date < '2025-06-26' else 1447,  # 伊斯兰历年份
                'month': key_info['hijri_month'],
                'day': key_info['hijri_day'],
                'month_name': self.hijri_month_names[key_info['hijri_month'] - 1],
                'event': key_info.get('event', '')
            }

        # 使用改进的算法计算伊斯兰历日期
        # 基于 Julian Day Number 的计算方法，并使用关键日期进行校准

        # 计算 Julian Day Number
        if month <= 2:
            year_adj = year - 1
            month_adj = month + 12
        else:
            year_adj = year
            month_adj = month

        # 格里高利历的 Julian Day 计算
        a = int(year_adj / 100)
        b = 2 - a + int(a / 4)
        jd = int(365.25 * (year_adj + 4716)) + int(30.6001 * (month_adj + 1)) + day + b - 1524.5

        # 伊斯兰历纪元开始于 622年7月16日 (Julian Day Number: 1948439)
        hijri_epoch = 1948439

        # 计算从伊斯兰历纪元开始的天数
        days_since_epoch = int(jd - hijri_epoch)

        # 使用关键日期进行校准
        # 2025年3月1日是斋月第一天 (1446年9月1日)
        ref_gregorian = date(2025, 3, 1)
        ref_hijri_year = 1446
        ref_hijri_month = 9
        ref_hijri_day = 1

        current_date = date(year, month, day)
        days_diff = (current_date - ref_gregorian).days

        # 从参考日期计算伊斯兰历日期
        hijri_year = ref_hijri_year
        hijri_month = ref_hijri_month
        hijri_day = ref_hijri_day + days_diff

        # 调整月份和年份
        while hijri_day > 30:  # 假设每月30天（简化）
            hijri_day -= 30
            hijri_month += 1
            if hijri_month > 12:
                hijri_month = 1
                hijri_year += 1

        while hijri_day < 1:
            hijri_month -= 1
            if hijri_month < 1:
                hijri_month = 12
                hijri_year -= 1
            hijri_day += 30

        # 确保月份在有效范围内
        if hijri_month < 1:
            hijri_month = 1
        elif hijri_month > 12:
            hijri_month = 12

        return {
            'year': hijri_year,
            'month': hijri_month,
            'day': hijri_day,
            'month_name': self.hijri_month_names[hijri_month - 1] if 1 <= hijri_month <= 12 else f"Month_{hijri_month}"
        }
    
    def get_calendar_page(self, year: int) -> Optional[BeautifulSoup]:
        """
        获取指定年份的日历页面

        Args:
            year: 年份

        Returns:
            BeautifulSoup对象或None
        """
        url = f"https://calendar-yearly.com/id/{year}"
        logger.info(f"正在获取 {year} 年日历页面: {url}")

        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            logger.info(f"成功获取页面，内容长度: {len(response.content)}")

            return soup

        except requests.RequestException as e:
            logger.error(f"获取页面失败: {e}")
            return None
    
    def extract_islamic_events_from_page(self, soup: BeautifulSoup, year: int) -> Dict[str, Dict]:
        """
        从页面中提取伊斯兰历事件信息

        Args:
            soup: BeautifulSoup对象
            year: 年份

        Returns:
            事件字典，键为公历日期，值为事件信息
        """
        events = {}

        # 查找所有包含伊斯兰历事件的文本
        page_text = soup.get_text()

        # 定义事件模式和对应的日期
        event_patterns = [
            (r'(\d{1,2})\.(\d{1,2})\.\s*Tahun Baru Islam', 'Islamic New Year'),
            (r'(\d{1,2})\.(\d{1,2})\.\s*Isra Mikraj', 'Isra and Mi\'raj'),
            (r'(\d{1,2})\.(\d{1,2})\.\s*Mulai Ramadhan', 'Start of Ramadan'),
            (r'(\d{1,2})\.(\d{1,2})\.\s*Hari Raya Idulfitri', 'Eid al-Fitr'),
            (r'(\d{1,2})\.(\d{1,2})\.\s*Hari Raya Idul Adha', 'Eid al-Adha'),
            (r'(\d{1,2})\.(\d{1,2})\.\s*Maulid Nabi Muhammad', 'Mawlid'),
        ]

        for pattern, event_name in event_patterns:
            matches = re.finditer(pattern, page_text)
            for match in matches:
                day = int(match.group(1))
                month = int(match.group(2))
                date_key = f"{year}-{month:02d}-{day:02d}"
                events[date_key] = {
                    'event': event_name,
                    'type': 'islamic',
                    'description': self.get_event_description(event_name)
                }

        return events

    def parse_calendar_structure(self, soup: BeautifulSoup, year: int) -> List[Dict]:
        """
        解析新版网站的日历结构

        Args:
            soup: BeautifulSoup对象
            year: 年份

        Returns:
            日历数据列表
        """
        calendar_data = []

        # 从页面文本中提取月份和日期信息
        # 新版网站使用不同的结构，需要解析文本内容

        page_text = soup.get_text()

        # 查找月份部分
        month_sections = re.findall(r'(Januari|Februari|Maret|April|Mei|Juni|Juli|Agustus|September|Oktober|November|Desember)\s+\d{4}(.*?)(?=(?:Januari|Februari|Maret|April|Mei|Juni|Juli|Agustus|September|Oktober|November|Desember)\s+\d{4}|$)', page_text, re.DOTALL)

        for month_name, month_content in month_sections:
            month_num = self.month_map.get(month_name)
            if not month_num:
                continue

            # 提取该月的日期
            # 查找数字模式，这些通常是日期
            day_matches = re.findall(r'\b(\d{1,2})\b', month_content)

            for day_str in day_matches:
                try:
                    day_num = int(day_str)
                    if 1 <= day_num <= 31:  # 有效的日期范围
                        # 验证日期是否有效
                        try:
                            test_date = date(year, month_num, day_num)

                            # 计算对应的伊斯兰历日期
                            hijri_date = self.gregorian_to_hijri_improved(year, month_num, day_num)

                            gregorian_date = f"{year}-{month_num:02d}-{day_num:02d}"

                            calendar_data.append({
                                'gregorian_date': gregorian_date,
                                'gregorian_day': day_num,
                                'gregorian_month': month_num,
                                'gregorian_year': year,
                                'gregorian_month_name': month_name,
                                'hijri_date': f"{hijri_date['day']} {hijri_date['month_name']} {hijri_date['year']}",
                                'hijri_day': hijri_date['day'],
                                'hijri_month': hijri_date['month'],
                                'hijri_year': hijri_date['year'],
                                'hijri_month_name': hijri_date['month_name']
                            })
                        except ValueError:
                            # 无效日期，跳过
                            continue
                except ValueError:
                    continue

        return calendar_data
    
    def generate_full_year_calendar(self, year: int) -> List[Dict]:
        """
        生成完整年份的日历数据
        使用改进的算法确保每一天都有对应的伊斯兰历日期

        Args:
            year: 年份

        Returns:
            完整年份的日历数据列表
        """
        calendar_data = []

        # 遍历一年中的每一天
        current_date = date(year, 1, 1)
        end_date = date(year, 12, 31)

        while current_date <= end_date:
            # 计算对应的伊斯兰历日期
            hijri_date = self.gregorian_to_hijri_improved(
                current_date.year,
                current_date.month,
                current_date.day
            )

            gregorian_date_str = current_date.strftime("%Y-%m-%d")
            month_name = list(self.month_map.keys())[current_date.month - 1]

            calendar_data.append({
                'gregorian_date': gregorian_date_str,
                'gregorian_day': current_date.day,
                'gregorian_month': current_date.month,
                'gregorian_year': current_date.year,
                'gregorian_month_name': month_name,
                'hijri_date': f"{hijri_date['day']} {hijri_date['month_name']} {hijri_date['year']}",
                'hijri_day': hijri_date['day'],
                'hijri_month': hijri_date['month'],
                'hijri_year': hijri_date['year'],
                'hijri_month_name': hijri_date['month_name']
            })

            current_date += timedelta(days=1)

        return calendar_data

    def validate_hijri_calculation(self, year: int, month: int, day: int) -> bool:
        """
        验证伊斯兰历计算的准确性

        Args:
            year: 公历年
            month: 公历月
            day: 公历日

        Returns:
            计算是否合理的布尔值
        """
        hijri_date = self.gregorian_to_hijri_improved(year, month, day)

        # 基本验证
        if not (1 <= hijri_date['day'] <= 30):
            return False
        if not (1 <= hijri_date['month'] <= 12):
            return False
        if not (1440 <= hijri_date['year'] <= 1500):  # 更新年份范围
            return False

        return True
    
    def get_event_description(self, event_name: str) -> str:
        """
        获取伊斯兰历节日的描述

        Args:
            event_name: 节日名称

        Returns:
            节日描述
        """
        descriptions = {
            'Islamic New Year': '伊斯兰历新年 (1 Muharram)',
            'Isra and Mi\'raj': '夜行登宵节，纪念先知穆罕默德的夜行和登宵',
            'Start of Ramadan': '斋月开始，穆斯林开始斋戒的月份',
            'Eid al-Fitr': '开斋节，斋月结束后的庆祝节日',
            'Eid al-Adha': '宰牲节，纪念易卜拉欣献祭的节日',
            'Mawlid': '先知穆罕默德诞辰纪念日',
            'Ramadan': '伊斯兰教斋月',
            'Muharram': '伊斯兰历第一个月，包含阿舒拉日'
        }

        for key, desc in descriptions.items():
            if key.lower() in event_name.lower():
                return desc

        return '伊斯兰历重要节日'
    
    def scrape_calendar_mapping(self, year: int) -> Dict:
        """
        抓取指定年份的公历-伊斯兰历映射关系
        结合网站信息和计算方法

        Args:
            year: 年份

        Returns:
            包含映射关系的字典
        """
        logger.info(f"开始抓取 {year} 年的公历-伊斯兰历映射关系")

        # 获取网页内容
        soup = self.get_calendar_page(year)

        # 生成完整年份的日历数据（使用改进的算法）
        logger.info("生成完整年份的日历数据...")
        all_calendar_data = self.generate_full_year_calendar(year)

        # 如果成功获取网页，提取伊斯兰历事件信息
        islamic_events = {}
        if soup:
            logger.info("从网页提取伊斯兰历事件信息...")
            islamic_events = self.extract_islamic_events_from_page(soup, year)
        else:
            logger.warning("无法获取网页，使用预设的关键日期信息")
            # 使用预设的关键日期
            for date_str, info in self.key_dates_2025.items():
                if date_str.startswith(str(year)):
                    islamic_events[date_str] = {
                        'event': info['event'],
                        'type': 'islamic',
                        'description': self.get_event_description(info['event'])
                    }

        # 转换为以公历日期为键的字典
        calendar_map = {}
        for data in all_calendar_data:
            gregorian_date = data['gregorian_date']

            calendar_map[gregorian_date] = {
                'gregorian_day': data['gregorian_day'],
                'gregorian_month': data['gregorian_month'],
                'gregorian_year': data['gregorian_year'],
                'gregorian_month_name': data['gregorian_month_name'],
                'hijri_date': data['hijri_date'],
                'hijri_day': data['hijri_day'],
                'hijri_month': data['hijri_month'],
                'hijri_year': data['hijri_year'],
                'hijri_month_name': data['hijri_month_name']
            }

            # 添加节日信息
            if gregorian_date in islamic_events:
                calendar_map[gregorian_date]['event'] = islamic_events[gregorian_date]['event']
                calendar_map[gregorian_date]['event_type'] = islamic_events[gregorian_date]['type']
                calendar_map[gregorian_date]['event_description'] = islamic_events[gregorian_date]['description']

        logger.info(f"成功生成 {len(calendar_map)} 个日期映射")
        logger.info(f"包含 {len(islamic_events)} 个伊斯兰历事件")

        return calendar_map
    
    def save_calendar_data(self, data: Dict, filename: str):
        """
        保存日历数据到JSON文件
        
        Args:
            data: 日历数据
            filename: 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")


def main():
    """主函数"""
    print("=== 公历-伊斯兰历映射关系抓取工具 ===")
    print("从 calendar-yearly.com 获取数据并结合计算方法")
    print()

    spider = CalendarSpider()

    # 抓取2025年的日历数据
    target_year = 2025
    calendar_data = spider.scrape_calendar_mapping(target_year)

    if calendar_data:
        print(f"\n=== {target_year}年 公历-伊斯兰历 映射关系 ===")

        # 显示关键日期的映射作为示例
        print("\n关键日期示例:")
        key_dates = ['2025-01-01', '2025-03-01', '2025-03-30', '2025-06-06', '2025-06-26', '2025-09-04', '2025-12-25']
        for date_key in key_dates:
            if date_key in calendar_data:
                data = calendar_data[date_key]
                event_info = f" ({data['event']})" if 'event' in data else ""
                print(f"  {date_key}: {data['hijri_date']}{event_info}")

        # 保存到文件
        filename = f"calendar_mapping_{target_year}.json"
        spider.save_calendar_data(calendar_data, filename)

        # 显示统计信息
        print(f"\n统计信息:")
        print(f"- 总映射数量: {len(calendar_data)}")
        print(f"- 数据已保存到: {filename}")

        # 显示伊斯兰历节日
        events = {k: v for k, v in calendar_data.items() if 'event' in v}
        if events:
            print(f"\n伊斯兰历重要节日 ({len(events)} 个):")
            for date, info in sorted(events.items()):
                description = info.get('event_description', '')
                print(f"  {date}: {info['event']}")
                if description:
                    print(f"    {description}")

        # 显示伊斯兰历月份统计
        print(f"\n伊斯兰历月份分布:")
        hijri_months = {}
        for data in calendar_data.values():
            month_name = data['hijri_month_name']
            hijri_months[month_name] = hijri_months.get(month_name, 0) + 1

        for month_name in spider.hijri_month_names:
            count = hijri_months.get(month_name, 0)
            print(f"  {month_name}: {count} 天")

        print(f"\n数据验证:")
        # 验证一些关键日期
        validation_dates = ['2025-03-01', '2025-06-26']
        for date_key in validation_dates:
            if date_key in calendar_data:
                data = calendar_data[date_key]
                is_valid = spider.validate_hijri_calculation(
                    data['gregorian_year'],
                    data['gregorian_month'],
                    data['gregorian_day']
                )
                status = "✓" if is_valid else "✗"
                print(f"  {date_key}: {status} {data['hijri_date']}")

    else:
        print("未能获取到日历数据")


if __name__ == "__main__":
    main()
