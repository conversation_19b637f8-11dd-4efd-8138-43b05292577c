#!/usr/bin/env python3
"""
测试脚本：验证公历-伊斯兰历映射关系的准确性
"""

import json
from datetime import datetime

def load_calendar_data(filename):
    """加载日历数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def test_key_dates():
    """测试关键日期的准确性"""
    print("=== 测试关键日期的准确性 ===")
    
    # 加载数据
    data = load_calendar_data('calendar_mapping_2025.json')
    
    # 定义已知的关键日期（基于伊斯兰历权威来源）
    key_dates = {
        '2025-01-01': {'expected_hijri': '2 Rajab 1446', 'description': '新年'},
        '2025-03-01': {'expected_hijri': '1 Ramadan 1446', 'description': '斋月开始'},
        '2025-03-30': {'expected_hijri': '1 Shawwal 1446', 'description': '开斋节'},
        '2025-06-06': {'expected_hijri': '10 Dhu al-Hijjah 1446', 'description': '宰牲节'},
        '2025-06-26': {'expected_hijri': '1 Muharram 1447', 'description': '伊斯兰历新年'},
        '2025-09-04': {'expected_hijri': '12 Rabi al-Awwal 1447', 'description': '先知诞辰'},
    }
    
    for date_str, expected in key_dates.items():
        if date_str in data:
            actual_hijri = data[date_str]['hijri_date']
            expected_hijri = expected['expected_hijri']
            status = "✓" if actual_hijri == expected_hijri else "✗"
            
            print(f"{status} {date_str} ({expected['description']})")
            print(f"  期望: {expected_hijri}")
            print(f"  实际: {actual_hijri}")
            if 'event' in data[date_str]:
                print(f"  事件: {data[date_str]['event']}")
            print()
        else:
            print(f"✗ {date_str}: 数据中未找到")

def test_month_transitions():
    """测试月份转换的连续性"""
    print("=== 测试月份转换的连续性 ===")
    
    data = load_calendar_data('calendar_mapping_2025.json')
    
    # 检查一些月份转换点
    transition_dates = [
        ('2025-02-28', '2025-03-01'),  # 2月到3月
        ('2025-03-30', '2025-03-31'),  # 斋月结束，开斋节
        ('2025-06-25', '2025-06-26'),  # 伊斯兰历年份转换
    ]
    
    for date1, date2 in transition_dates:
        if date1 in data and date2 in data:
            hijri1 = data[date1]
            hijri2 = data[date2]
            
            print(f"{date1}: {hijri1['hijri_date']}")
            print(f"{date2}: {hijri2['hijri_date']}")
            
            # 检查日期连续性
            if hijri1['hijri_year'] == hijri2['hijri_year'] and hijri1['hijri_month'] == hijri2['hijri_month']:
                day_diff = hijri2['hijri_day'] - hijri1['hijri_day']
                if day_diff == 1:
                    print("✓ 日期连续")
                else:
                    print(f"✗ 日期不连续，差值: {day_diff}")
            else:
                print("→ 月份或年份转换")
            print()

def test_islamic_events():
    """测试伊斯兰历事件"""
    print("=== 伊斯兰历重要事件 ===")
    
    data = load_calendar_data('calendar_mapping_2025.json')
    
    events = {}
    for date_str, info in data.items():
        if 'event' in info:
            events[date_str] = info
    
    print(f"找到 {len(events)} 个伊斯兰历事件:")
    for date_str in sorted(events.keys()):
        info = events[date_str]
        print(f"  {date_str}: {info['event']}")
        print(f"    伊斯兰历: {info['hijri_date']}")
        if 'event_description' in info:
            print(f"    描述: {info['event_description']}")
        print()

def test_data_completeness():
    """测试数据完整性"""
    print("=== 数据完整性测试 ===")
    
    data = load_calendar_data('calendar_mapping_2025.json')
    
    # 检查是否包含完整的一年
    expected_days = 365  # 2025年不是闰年
    actual_days = len(data)
    
    print(f"期望天数: {expected_days}")
    print(f"实际天数: {actual_days}")
    print(f"完整性: {'✓' if actual_days == expected_days else '✗'}")
    
    # 检查伊斯兰历月份分布
    hijri_months = {}
    for info in data.values():
        month_name = info['hijri_month_name']
        hijri_months[month_name] = hijri_months.get(month_name, 0) + 1
    
    print(f"\n伊斯兰历月份分布:")
    total_days = 0
    for month_name in ['Muharram', 'Safar', 'Rabi al-Awwal', 'Rabi al-Thani',
                       'Jumada al-Awwal', 'Jumada al-Thani', 'Rajab', 'Shaban',
                       'Ramadan', 'Shawwal', 'Dhu al-Qadah', 'Dhu al-Hijjah']:
        count = hijri_months.get(month_name, 0)
        total_days += count
        print(f"  {month_name}: {count} 天")
    
    print(f"\n总计: {total_days} 天")

if __name__ == "__main__":
    print("公历-伊斯兰历映射关系验证测试")
    print("=" * 50)
    print()
    
    try:
        test_key_dates()
        test_month_transitions()
        test_islamic_events()
        test_data_completeness()
        
        print("测试完成！")
        
    except FileNotFoundError:
        print("错误: 找不到 calendar_mapping_2025.json 文件")
        print("请先运行 calendar_spider.py 生成数据文件")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
