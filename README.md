# 公历-伊斯兰历映射关系抓取工具

这个项目包含一个重写的 Python 脚本，用于从 [calendar-yearly.com](https://calendar-yearly.com/id/2025) 网站获取公历和伊斯兰历的对应关系。

## 🚀 功能特点

### 🔄 改进的转换算法

- **结合网站数据和计算方法**：不仅从网站抓取信息，还使用改进的算法计算伊斯兰历日期
- **关键日期校准**：使用已知的重要伊斯兰历节日作为参考点，提高计算准确性
- **完整年份覆盖**：生成一年中每一天的公历-伊斯兰历对应关系

### 📅 准确的日期映射

- **2025 年完整映射**：包含 365 天的完整日期对应关系
- **伊斯兰历事件识别**：自动识别和标记重要的伊斯兰历节日
- **多语言支持**：支持印尼语月份名称和伊斯兰历月份名称

### 🎯 重要节日识别

脚本能够识别以下重要的伊斯兰历节日：

- **伊斯兰历新年** (1 Muharram) - 2025-06-26
- **夜行登宵节** (Is<PERSON> and Mi'raj) - 2025-01-27
- **斋月开始** (Start of Ramadan) - 2025-03-01
- **开斋节** (Eid al-Fitr) - 2025-03-30
- **宰牲节** (Eid al-Adha) - 2025-06-06
- **先知诞辰** (Mawlid) - 2025-09-04

## 📦 安装和使用

### 环境要求

```bash
pip install requests beautifulsoup4
```

### 运行脚本

```bash
python scripts/calendar_spider.py
```

### 测试数据准确性

```bash
python test_calendar.py
```

## 📊 输出文件

### calendar_mapping_2025.json

包含完整的 2025 年公历-伊斯兰历映射关系，格式如下：

```json
{
  "2025-01-01": {
    "gregorian_day": 1,
    "gregorian_month": 1,
    "gregorian_year": 2025,
    "gregorian_month_name": "Januari",
    "hijri_date": "2 Rajab 1446",
    "hijri_day": 2,
    "hijri_month": 7,
    "hijri_year": 1446,
    "hijri_month_name": "Rajab"
  },
  "2025-03-01": {
    "gregorian_day": 1,
    "gregorian_month": 3,
    "gregorian_year": 2025,
    "gregorian_month_name": "Maret",
    "hijri_date": "1 Ramadan 1446",
    "hijri_day": 1,
    "hijri_month": 9,
    "hijri_year": 1446,
    "hijri_month_name": "Ramadan",
    "event": "Start of Ramadan",
    "event_type": "islamic",
    "event_description": "斋月开始，穆斯林开始斋戒的月份"
  }
}
```

## 🔧 技术实现

### 核心算法

1. **基准日期校准**：使用 2025 年 3 月 1 日（斋月第一天）作为参考点
2. **Julian Day 计算**：结合格里高利历的 Julian Day Number 进行精确计算
3. **关键日期验证**：使用已知的伊斯兰历重要节日验证计算结果

### 数据来源

- **网站抓取**：从 calendar-yearly.com 获取伊斯兰历事件信息
- **算法计算**：使用改进的公历-伊斯兰历转换算法
- **关键日期**：预设的 2025 年重要伊斯兰历节日作为校准参考

### 准确性验证

脚本包含多层验证机制：

- ✅ 关键日期匹配验证
- ✅ 月份转换连续性检查
- ✅ 数据完整性测试
- ✅ 伊斯兰历月份分布验证

## 📈 测试结果

根据测试脚本的验证结果：

### ✅ 关键日期准确性

- 2025-01-01: 2 Rajab 1446 ✓
- 2025-03-01: 1 Ramadan 1446 ✓ (斋月开始)
- 2025-03-30: 1 Shawwal 1446 ✓ (开斋节)
- 2025-06-06: 10 Dhu al-Hijjah 1446 ✓ (宰牲节)
- 2025-06-26: 1 Muharram 1447 ✓ (伊斯兰历新年)
- 2025-09-04: 12 Rabi al-Awwal 1447 ✓ (先知诞辰)

### 📊 数据统计

- **总映射数量**: 365 天 (完整年份)
- **伊斯兰历事件**: 7 个重要节日
- **月份分布**: 12 个伊斯兰历月份，天数分布合理

## 📁 文件结构

```
├── scripts/
│   └── calendar_spider.py      # 主要的爬虫脚本
├── test_calendar.py            # 测试验证脚本
├── calendar_mapping_2025.json  # 生成的映射数据
└── README.md                   # 项目说明文档
```

## 💡 使用示例

### 基本使用

```python
from scripts.calendar_spider import CalendarSpider

# 创建爬虫实例
spider = CalendarSpider()

# 抓取2025年的日历数据
calendar_data = spider.scrape_calendar_mapping(2025)

# 保存到文件
spider.save_calendar_data(calendar_data, "calendar_2025.json")

# 查看特定日期的伊斯兰历信息
date_info = calendar_data["2025-03-01"]
print(f"公历 2025-03-01 对应伊斯兰历: {date_info['hijri_date']}")
```

### 查询伊斯兰历事件

```python
# 查找所有伊斯兰历事件
events = {k: v for k, v in calendar_data.items() if 'event' in v}
for date, info in events.items():
    print(f"{date}: {info['event']} - {info['hijri_date']}")
```

## ⚠️ 注意事项

1. **网络连接**：脚本需要访问 calendar-yearly.com，请确保网络连接正常
2. **数据准确性**：伊斯兰历日期可能因地区和计算方法而略有差异
3. **年份限制**：当前版本专门针对 2025 年优化，其他年份可能需要调整关键日期
4. **更新频率**：建议定期更新关键日期以保持准确性
5. **网站访问**：请遵守网站的使用条款，避免过于频繁的请求

## 🤝 贡献

欢迎提交问题报告和改进建议！如果发现日期计算不准确，请提供具体的日期和期望结果。

### 如何贡献

1. Fork 这个仓库
2. 创建你的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📝 更新日志

### v2.0.0 (2025-08-14)

- 🔄 完全重写脚本，改进算法准确性
- ✨ 添加关键日期校准功能
- 📅 支持完整年份的日期映射
- 🎯 改进伊斯兰历事件识别
- ✅ 添加全面的测试验证
- 📊 优化数据输出格式

### v1.0.0

- 🎉 初始版本
- 📅 基本的公历-伊斯兰历转换
- 🕌 伊斯兰历节日提取
- 💾 JSON 格式数据输出
