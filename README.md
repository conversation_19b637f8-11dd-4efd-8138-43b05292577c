# Data Spider

一个用于数据抓取的Python脚本集合。

## 功能特性

### 日历爬虫 (Calendar Spider)

从 [calendar-yearly.com](https://calendar-yearly.com) 网站抓取公历和伊斯兰历的对应关系。

#### 主要功能

- **公历-伊斯兰历转换**: 使用改进的算法计算公历日期对应的伊斯兰历日期
- **伊斯兰历节日提取**: 自动识别和提取网站上的伊斯兰历节日信息
- **数据验证**: 内置验证功能确保计算结果的准确性
- **多格式输出**: 支持JSON格式输出，便于后续处理

#### 使用方法

```bash
# 运行主爬虫脚本
uv run python scripts/calendar_spider.py

# 运行测试脚本
uv run python scripts/test_calendar_spider.py
```

#### 输出示例

```json
{
  "2025-01-01": {
    "hijri_date": "13 Rajab 1447",
    "hijri_day": 13,
    "hijri_month": 7,
    "hijri_year": 1447,
    "hijri_month_name": "Rajab"
  },
  "2025-03-01": {
    "hijri_date": "13 Ramadan 1447",
    "hijri_day": 13,
    "hijri_month": 9,
    "hijri_year": 1447,
    "hijri_month_name": "Ramadan",
    "event": "Mulai Ramadhan",
    "event_type": "islamic",
    "description": "伊斯兰教斋月，穆斯林进行斋戒的月份"
  }
}
```

#### 支持的伊斯兰历节日

- **Ramadhan** (斋月): 伊斯兰教斋月，穆斯林进行斋戒的月份
- **Idulfitri** (开斋节): 斋月结束后的庆祝节日
- **Idul Adha** (宰牲节): 纪念易卜拉欣献祭的节日
- **Maulid Nabi** (先知诞辰): 先知穆罕默德诞辰纪念日
- **Isra Mikraj** (夜行登宵节): 纪念先知夜行和登宵
- **Tahun Baru Islam** (伊斯兰历新年): 伊斯兰历新年

### 网页爬虫 (Web Scraper)

通用的网页爬虫工具，支持：

- 网页内容抓取
- 链接提取
- 文本内容提取
- 数据保存

## 安装和设置

### 环境要求

- Python 3.8+
- uv (Python包管理器)

### 安装依赖

```bash
# 安装项目依赖
uv sync

# 安装开发依赖
uv sync --extra dev
```

### 项目结构

```
data-spider/
├── pyproject.toml          # 项目配置
├── README.md              # 项目说明
├── scripts/               # 脚本目录
│   ├── __init__.py
│   ├── calendar_spider.py # 日历爬虫
│   ├── web_scraper.py     # 网页爬虫
│   ├── data_utils.py      # 数据工具
│   └── test_calendar_spider.py # 测试脚本
├── tests/                 # 测试目录
│   ├── __init__.py
│   ├── test_data_utils.py
│   └── test_web_scraper.py
└── uv.lock               # 依赖锁定文件
```

## 使用示例

### 日历爬虫示例

```python
from scripts.calendar_spider import CalendarSpider

# 创建爬虫实例
spider = CalendarSpider()

# 抓取2025年的日历数据
calendar_data = spider.scrape_calendar_mapping(2025)

# 保存到文件
spider.save_calendar_data(calendar_data, "calendar_2025.json")

# 查看特定日期的伊斯兰历信息
date_info = calendar_data["2025-03-01"]
print(f"公历 2025-03-01 对应伊斯兰历: {date_info['hijri_date']}")
```

### 网页爬虫示例

```python
from scripts.web_scraper import WebScraper

# 创建爬虫实例
scraper = WebScraper()

# 获取网页内容
soup = scraper.get_page("https://example.com")

if soup:
    # 提取标题
    title = scraper.extract_text_content(soup, "h1")
    print(f"页面标题: {title}")
    
    # 提取链接
    links = scraper.extract_links(soup, "https://example.com")
    print(f"找到 {len(links)} 个链接")
```

## 开发

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_calendar_spider.py
```

### 代码格式化

```bash
# 格式化代码
uv run black scripts/ tests/

# 排序导入
uv run isort scripts/ tests/
```

### 代码检查

```bash
# 运行代码检查
uv run flake8 scripts/ tests/
```

## 注意事项

1. **网站访问**: 请遵守网站的robots.txt和使用条款
2. **请求频率**: 避免过于频繁的请求，建议添加适当的延迟
3. **数据准确性**: 伊斯兰历计算基于算法，可能与官方数据有细微差异
4. **错误处理**: 脚本包含基本的错误处理，但建议在生产环境中添加更多异常处理

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 更新日志

### v0.1.0
- 初始版本
- 添加日历爬虫功能
- 添加网页爬虫功能
- 支持公历-伊斯兰历转换
- 支持伊斯兰历节日提取
