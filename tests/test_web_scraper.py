"""
Tests for web_scraper module
"""

import pytest
from scripts.web_scraper import WebScraper
from bs4 import BeautifulSoup


class TestWebScraper:
    """测试WebScraper类"""
    
    def test_init(self):
        """测试初始化"""
        scraper = WebScraper()
        assert scraper.session is not None
        assert 'User-Agent' in scraper.headers
    
    def test_init_with_custom_headers(self):
        """测试自定义请求头初始化"""
        custom_headers = {'X-Custom': 'test'}
        scraper = WebScraper(headers=custom_headers)
        assert scraper.headers['X-Custom'] == 'test'
    
    def test_extract_links(self):
        """测试链接提取"""
        scraper = WebScraper()
        
        # 创建测试HTML
        html = '''
        <html>
            <body>
                <a href="https://example.com">Example</a>
                <a href="/relative/path">Relative</a>
                <a href="http://test.com">Test</a>
            </body>
        </html>
        '''
        soup = BeautifulSoup(html, 'html.parser')
        
        links = scraper.extract_links(soup, "https://base.com")
        
        assert "https://example.com" in links
        assert "http://test.com" in links
        assert "https://base.com/relative/path" in links
    
    def test_extract_text_content(self):
        """测试文本内容提取"""
        scraper = WebScraper()
        
        html = '<html><body><h1>Test Title</h1><p>Test content</p></body></html>'
        soup = BeautifulSoup(html, 'html.parser')
        
        title = scraper.extract_text_content(soup, "h1")
        assert title == "Test Title"
        
        content = scraper.extract_text_content(soup, "p")
        assert content == "Test content"
    
    def test_extract_text_content_not_found(self):
        """测试未找到元素的情况"""
        scraper = WebScraper()
        
        html = '<html><body><p>Test content</p></body></html>'
        soup = BeautifulSoup(html, 'html.parser')
        
        result = scraper.extract_text_content(soup, "h1")
        assert result == ""
