"""
Tests for data_utils module
"""

import pytest
import pandas as pd
import json
import tempfile
import os
from scripts.data_utils import DataProcessor


class TestDataProcessor:
    """测试DataProcessor类"""
    
    def test_init(self):
        """测试初始化"""
        processor = DataProcessor()
        assert processor is not None
    
    def test_write_and_read_json(self):
        """测试JSON文件写入和读取"""
        processor = DataProcessor()
        
        test_data = {
            'name': 'test',
            'value': 123,
            'list': [1, 2, 3]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            # 写入JSON
            processor.write_json(test_data, temp_file)
            
            # 读取JSON
            loaded_data = processor.read_json(temp_file)
            
            assert loaded_data == test_data
        finally:
            os.unlink(temp_file)
    
    def test_write_and_read_csv(self):
        """测试CSV文件写入和读取"""
        processor = DataProcessor()
        
        test_data = {
            'name': ['<PERSON>', '<PERSON>', '<PERSON>'],
            'age': [25, 30, 35],
            'city': ['Beijing', 'Shanghai', 'Guangzhou']
        }
        df = pd.DataFrame(test_data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            temp_file = f.name
        
        try:
            # 写入CSV
            processor.write_csv(df, temp_file)
            
            # 读取CSV
            loaded_df = processor.read_csv(temp_file)
            
            pd.testing.assert_frame_equal(df, loaded_df)
        finally:
            os.unlink(temp_file)
    
    def test_clean_data(self):
        """测试数据清洗"""
        processor = DataProcessor()
        
        # 创建包含重复行和空值的数据
        test_data = {
            'name': ['Alice', 'Bob', 'Alice', 'David', None],
            'age': [25, 30, 25, 28, None],
            'city': ['Beijing', 'Shanghai', 'Beijing', 'Shenzhen', None]
        }
        df = pd.DataFrame(test_data)
        
        # 清洗数据
        cleaned_df = processor.clean_data(df)
        
        # 检查是否删除了重复行
        assert len(cleaned_df) < len(df)
        
        # 检查是否删除了全为空的行
        assert not cleaned_df.isnull().all(axis=1).any()
    
    def test_filter_data(self):
        """测试数据过滤"""
        processor = DataProcessor()
        
        test_data = {
            'name': ['Alice', 'Bob', 'Charlie', 'David'],
            'age': [25, 30, 35, 28],
            'city': ['Beijing', 'Shanghai', 'Guangzhou', 'Shenzhen']
        }
        df = pd.DataFrame(test_data)
        
        # 过滤条件
        conditions = {'age': 25}
        filtered_df = processor.filter_data(df, conditions)
        
        assert len(filtered_df) == 1
        assert filtered_df.iloc[0]['name'] == 'Alice'
    
    def test_get_summary_stats(self):
        """测试获取统计信息"""
        processor = DataProcessor()
        
        test_data = {
            'name': ['Alice', 'Bob', 'Charlie'],
            'age': [25, 30, 35],
            'city': ['Beijing', 'Shanghai', 'Guangzhou']
        }
        df = pd.DataFrame(test_data)
        
        stats = processor.get_summary_stats(df)
        
        assert 'shape' in stats
        assert 'columns' in stats
        assert 'dtypes' in stats
        assert 'missing_values' in stats
        assert stats['shape'] == (3, 3)
        assert len(stats['columns']) == 3
